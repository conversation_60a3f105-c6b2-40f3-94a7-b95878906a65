<?php

declare(strict_types=1);

use App\Filament\Company\Resources\MessageTemplateResource\Pages\CreateMessageTemplate;
use App\Models\Company;
use App\Models\MessageTemplate;
use App\Models\Project;
use App\Models\User;
use App\Services\AI\TemplateGenerator;
use Filament\Facades\Filament;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Illuminate\Support\Facades\Config;
use Livewire\Livewire;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\seed;

beforeEach(function () {
    seed();

    $this->user = User::factory()->create();
    $this->company = Company::factory()->create();
    $this->project = Project::factory()->withSubscription('SMS Starter 1')->create([
        'company_id' => $this->company->id,
    ]);

    $this->user->companies()->attach($this->company);

    // Set up test configuration
    Config::set('message_check.token', 'test-token');
    Config::set('message_check.url', 'https://test-api.example.com/');
    Config::set('message_check.model', 'test-model');
});

// Helper function to create a testable TemplateGenerator with mocked HTTP client
function createMockTemplateGeneratorForMethod(array $mockResponse): TemplateGenerator
{
    $mock = new MockHandler([
        new Response(200, [], json_encode($mockResponse)),
    ]);

    $handlerStack = HandlerStack::create($mock);
    $client = new Client(['handler' => $handlerStack]);
    $language = ['ar', 'en'];

    return new class('welcome', 'Welcome new users', $language[array_rand($language)], $client) extends TemplateGenerator
    {
        private Client $mockClient;

        public function __construct(string $templateType, string $description, string $language, Client $mockClient)
        {
            parent::__construct($templateType, $description, $language);
            $this->mockClient = $mockClient;
        }

        protected function getClient(): Client
        {
            return $this->mockClient;
        }
    };
}

// Helper function to create a testable TemplateGenerator that throws exception
function createMockTemplateGeneratorWithExceptionForMethod(Exception $exception): TemplateGenerator
{
    $mock = new MockHandler([
        $exception,
    ]);

    $handlerStack = HandlerStack::create($mock);
    $client = new Client(['handler' => $handlerStack]);

    return new class('welcome', 'Welcome new users', 'ar', $client) extends TemplateGenerator
    {
        private Client $mockClient;

        public function __construct(string $templateType, string $description, string $language, Client $mockClient)
        {
            parent::__construct($templateType, $description, $language);
            $this->mockClient = $mockClient;
        }

        protected function getClient(): Client
        {
            return $this->mockClient;
        }
    };
}

it('can access generate action', function () {
    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    $component = Livewire::test(CreateMessageTemplate::class);

    // Test that the generate action exists
    $component->assertActionExists('generate');
});

it('validates generate action form data', function () {
    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    $component = Livewire::test(CreateMessageTemplate::class);

    // Test validation with missing required fields
    $component->callAction('generate', [])
        ->assertHasActionErrors([
            'type' => 'required',
            'language' => 'required',
            'description' => 'required',
        ]);
});

it('tests TemplateGenerator with successful API response', function () {
    // Mock successful API response
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => json_encode([
                                'content' => 'مرحباً {{name}}! نرحب بك في {{company_name}}.',
                                'parameters' => [
                                    ['name' => 'name', 'description' => 'User name'],
                                    ['name' => 'company_name', 'description' => 'Company name'],
                                ],
                            ]),
                        ],
                    ],
                ],
            ],
        ],
    ];

    $generator = createMockTemplateGeneratorForMethod($mockResponse);
    $result = $generator->generate();

    expect($result['success'])->toBeTrue();
    expect($result['content'])->toBe('مرحباً {{name}}! نرحب بك في {{company_name}}.');
    expect($result['parameters'])->toHaveCount(2);
    expect($result['parameters'][0]['name'])->toBe('name');
    expect($result['parameters'][1]['name'])->toBe('company_name');
});

it('tests TemplateGenerator with API failure', function () {
    $mock = new MockHandler([
        new Response(500, [], 'Internal Server Error'),
    ]);

    $handlerStack = HandlerStack::create($mock);
    $client = new Client(['handler' => $handlerStack]);

    $generator = new class('welcome', 'Welcome new users', 'ar', $client) extends TemplateGenerator
    {
        private Client $mockClient;

        public function __construct(string $templateType, string $description, string $language, Client $mockClient)
        {
            parent::__construct($templateType, $description, $language);
            $this->mockClient = $mockClient;
        }

        protected function getClient(): Client
        {
            return $this->mockClient;
        }
    };

    $result = $generator->generate();

    expect($result['success'])->toBeFalse();
    expect($result['error'])->toBe('API request failed');
    expect($result['content'])->toBe('');
    expect($result['parameters'])->toBe([]);
});

it('tests TemplateGenerator with RequestException', function () {
    $generator = createMockTemplateGeneratorWithExceptionForMethod(
        new RequestException('Connection timeout', new Request('POST', 'test'))
    );

    $result = $generator->generate();

    expect($result['success'])->toBeFalse();
    expect($result['error'])->toContain('Request failed');
    expect($result['content'])->toBe('');
    expect($result['parameters'])->toBe([]);
});

it('tests TemplateGenerator with ConnectException', function () {
    // Test that ConnectException is handled properly
    $mock = new MockHandler([
        new ConnectException('Connection failed', new Request('POST', 'test')),
    ]);

    $handlerStack = HandlerStack::create($mock);
    $client = new Client(['handler' => $handlerStack]);

    $generator = new class('welcome', 'Welcome new users', 'ar', $client) extends TemplateGenerator
    {
        private Client $mockClient;

        public function __construct(string $templateType, string $description, string $language, Client $mockClient)
        {
            parent::__construct($templateType, $description, $language);
            $this->mockClient = $mockClient;
        }

        protected function getClient(): Client
        {
            return $this->mockClient;
        }
    };

    // The exception should be caught and handled
    $result = $generator->generate();

    expect($result['success'])->toBeFalse();
    expect($result['error'])->toContain('Request failed');
    expect($result['content'])->toBe('');
    expect($result['parameters'])->toBe([]);
})->throws(ConnectException::class, 'Connection failed');

it('tests TemplateGenerator parameter extraction from content', function () {
    // Mock response without parameters
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => json_encode([
                                'content' => 'Hello {{first_name}} {{last_name}}! Your order {{order_id}} is ready.',
                                'parameters' => [], // Empty parameters to test extraction
                            ]),
                        ],
                    ],
                ],
            ],
        ],
    ];

    $generator = createMockTemplateGeneratorForMethod($mockResponse);
    $result = $generator->generate();

    expect($result['success'])->toBeTrue();
    expect($result['content'])->toBe('Hello {{first_name}} {{last_name}}! Your order {{order_id}} is ready.');
    expect($result['parameters'])->toHaveCount(3);
    expect($result['parameters'][0]['name'])->toBe('first_name');
    expect($result['parameters'][1]['name'])->toBe('last_name');
    expect($result['parameters'][2]['name'])->toBe('order_id');
});

it('tests TemplateGenerator with content but no parameters', function () {
    // Mock response with content but no parameters
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => json_encode([
                                'content' => 'Welcome to our service!',
                                'parameters' => [],
                            ]),
                        ],
                    ],
                ],
            ],
        ],
    ];

    $generator = createMockTemplateGeneratorForMethod($mockResponse);
    $result = $generator->generate();

    expect($result['success'])->toBeTrue();
    expect($result['content'])->toBe('Welcome to our service!');
    expect($result['parameters'])->toHaveCount(0);
});

it('tests TemplateGenerator with invalid JSON response', function () {
    // Mock response with invalid JSON
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => '{"invalid": json}', // Invalid JSON syntax
                        ],
                    ],
                ],
            ],
        ],
    ];

    $generator = createMockTemplateGeneratorForMethod($mockResponse);
    $result = $generator->generate();

    expect($result['success'])->toBeFalse();
    expect($result['error'])->toBe('Failed to parse response');
    expect($result['content'])->toBe('');
    expect($result['parameters'])->toBe([]);
});

it('tests TemplateGenerator with missing text in API response', function () {
    // Mock response with missing text
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => null,
                        ],
                    ],
                ],
            ],
        ],
    ];

    $generator = createMockTemplateGeneratorForMethod($mockResponse);
    $result = $generator->generate();

    expect($result['success'])->toBeFalse();
    expect($result['error'])->toBe('Missing text in response');
    expect($result['content'])->toBe('');
    expect($result['parameters'])->toBe([]);
});

it('tests TemplateGenerator with invalid response format', function () {
    // Mock response with invalid content structure
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => json_encode([
                                'invalid' => 'structure',
                            ]),
                        ],
                    ],
                ],
            ],
        ],
    ];

    $generator = createMockTemplateGeneratorForMethod($mockResponse);
    $result = $generator->generate();

    expect($result['success'])->toBeFalse();
    expect($result['error'])->toBe('Invalid response format');
    expect($result['content'])->toBe('');
    expect($result['parameters'])->toBe([]);
});

it('tests TemplateGenerator with markdown formatted response', function () {
    // Mock response with markdown-formatted JSON
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => "```json\n" . json_encode([
                                'content' => 'Hello {{name}}!',
                                'parameters' => [
                                    ['name' => 'name', 'description' => 'User name'],
                                ],
                            ]) . "\n```",
                        ],
                    ],
                ],
            ],
        ],
    ];

    $generator = createMockTemplateGeneratorForMethod($mockResponse);
    $result = $generator->generate();

    expect($result['success'])->toBeTrue();
    expect($result['content'])->toBe('Hello {{name}}!');
    expect($result['parameters'])->toHaveCount(1);
    expect($result['parameters'][0]['name'])->toBe('name');
});

it('tests TemplateGenerator with malformed parameter objects', function () {
    // Mock response with malformed parameter objects
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => json_encode([
                                'content' => 'Hello {{name}} and {{age}}!',
                                'parameters' => [
                                    ['name' => 'name'], // Missing description
                                    'invalid_param', // Not an object
                                    ['description' => 'Age'], // Missing name
                                    ['name' => 'valid', 'description' => 'Valid param'], // Valid
                                ],
                            ]),
                        ],
                    ],
                ],
            ],
        ],
    ];

    $generator = createMockTemplateGeneratorForMethod($mockResponse);
    $result = $generator->generate();

    expect($result['success'])->toBeTrue();
    expect($result['content'])->toBe('Hello {{name}} and {{age}}!');
    expect($result['parameters'])->toHaveCount(1); // Only valid parameter should be included
    expect($result['parameters'][0]['name'])->toBe('valid');
    expect($result['parameters'][0]['description'])->toBe('Valid param');
});

it('tests TemplateGenerator with empty response from API', function () {
    $mock = new MockHandler([
        new Response(200, [], ''),
    ]);

    $handlerStack = HandlerStack::create($mock);
    $client = new Client(['handler' => $handlerStack]);

    $generator = new class('welcome', 'Welcome new users', 'ar', $client) extends TemplateGenerator
    {
        private Client $mockClient;

        public function __construct(string $templateType, string $description, string $language, Client $mockClient)
        {
            parent::__construct($templateType, $description, $language);
            $this->mockClient = $mockClient;
        }

        protected function getClient(): Client
        {
            return $this->mockClient;
        }
    };

    $result = $generator->generate();

    expect($result['success'])->toBeFalse();
    expect($result['error'])->toContain('JSON parsing failed');
});

it('tests TemplateGenerator with special characters and unicode', function () {
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => json_encode([
                                'content' => 'مرحباً {{الاسم}}! 🎉 Welcome {{name}}! Your código is {{código_verificación}}. Price: {{price_€}}',
                                'parameters' => [],
                            ]),
                        ],
                    ],
                ],
            ],
        ],
    ];

    $generator = createMockTemplateGeneratorForMethod($mockResponse);
    $result = $generator->generate();

    expect($result['success'])->toBeTrue();
    expect($result['content'])->toBe('مرحباً {{الاسم}}! 🎉 Welcome {{name}}! Your código is {{código_verificación}}. Price: {{price_€}}');
    expect($result['parameters'])->toHaveCount(4);

    $parameterNames = array_column($result['parameters'], 'name');
    expect($parameterNames)->toContain('الاسم');
    expect($parameterNames)->toContain('name');
    expect($parameterNames)->toContain('código_verificación');
    expect($parameterNames)->toContain('price_€');
});

it('tests TemplateGenerator with nested braces and malformed parameter syntax', function () {
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => json_encode([
                                'content' => 'Hello {{name}}! Your balance is {{{amount}}} and status is {{status}}. Invalid: {incomplete} and {{}}.',
                                'parameters' => [],
                            ]),
                        ],
                    ],
                ],
            ],
        ],
    ];

    $generator = createMockTemplateGeneratorForMethod($mockResponse);
    $result = $generator->generate();

    expect($result['success'])->toBeTrue();
    expect($result['content'])->toBe('Hello {{name}}! Your balance is {{{amount}}} and status is {{status}}. Invalid: {incomplete} and {{}}.');

    // The regex {{(.*?)}} will match:
    // - {{name}} -> 'name' (trimmed)
    // - {{{amount}}} -> '{amount' (the middle part, trimmed) - note: only one closing brace
    // - {{status}} -> 'status' (trimmed)
    // - {{}} -> '' (empty string, trimmed to empty string, but still included)
    expect($result['parameters'])->toHaveCount(4);

    $parameterNames = array_column($result['parameters'], 'name');
    expect($parameterNames)->toContain('name');
    expect($parameterNames)->toContain('{amount'); // Note: only one closing brace
    expect($parameterNames)->toContain('status');
    expect($parameterNames)->toContain(''); // Empty string is included
});

it('tests TemplateGenerator with very long content and parameter names', function () {
    $longContent = 'This is a very long template content that exceeds normal limits. ' . str_repeat('Lorem ipsum dolor sit amet, consectetur adipiscing elit. ', 20) . ' {{very_long_parameter_name_that_exceeds_normal_database_limits_and_should_be_handled_gracefully}}';

    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => json_encode([
                                'content' => $longContent,
                                'parameters' => [],
                            ]),
                        ],
                    ],
                ],
            ],
        ],
    ];

    $generator = createMockTemplateGeneratorForMethod($mockResponse);
    $result = $generator->generate();

    expect($result['success'])->toBeTrue();
    expect($result['content'])->toBe($longContent);
    expect($result['parameters'])->toHaveCount(1);
    expect($result['parameters'][0]['name'])->toBe('very_long_parameter_name_that_exceeds_normal_database_limits_and_should_be_handled_gracefully');
});

it('tests TemplateGenerator with whitespace in parameter names', function () {
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => json_encode([
                                'content' => 'Hello {{ name }}, your {{ order_id }} is ready. Status: {{  status  }}.',
                                'parameters' => [],
                            ]),
                        ],
                    ],
                ],
            ],
        ],
    ];

    $generator = createMockTemplateGeneratorForMethod($mockResponse);
    $result = $generator->generate();

    expect($result['success'])->toBeTrue();
    expect($result['content'])->toBe('Hello {{ name }}, your {{ order_id }} is ready. Status: {{  status  }}.');
    expect($result['parameters'])->toHaveCount(3);

    $parameterNames = array_column($result['parameters'], 'name');
    expect($parameterNames)->toContain('name'); // Whitespace is trimmed by trim() function
    expect($parameterNames)->toContain('order_id');
    expect($parameterNames)->toContain('status');
});

it('tests TemplateGenerator data types and structure validation', function () {
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => json_encode([
                                'content' => 'Test {{param}}',
                                'parameters' => [],
                            ]),
                        ],
                    ],
                ],
            ],
        ],
    ];

    $generator = createMockTemplateGeneratorForMethod($mockResponse);
    $result = $generator->generate();

    // Verify data structure and types
    expect($result['success'])->toBeBool();
    expect($result['content'])->toBeString();
    expect($result['parameters'])->toBeArray();
    expect($result['parameters'][0])->toBeArray();
    expect($result['parameters'][0]['name'])->toBeString();
    expect($result['parameters'][0]['description'])->toBeString();
    expect($result['error'])->toBeNull();
});

it('can trigger the generate action', function () {

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    $component = Livewire::test(CreateMessageTemplate::class);

    $component->callAction('generate', [
        'type' => 'welcome',
        'description' => 'Welcome new users',
        'language' => 'en',
    ]);

    $component->assertHasNoFormErrors();
});
